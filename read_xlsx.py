#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 pandas 读取 xlsx 数据的程序
"""

import pandas as pd
import os
import sys

def read_xlsx_file(file_path, sheet_name=None):
    """
    读取 xlsx 文件
    
    Args:
        file_path (str): Excel 文件路径
        sheet_name (str, optional): 工作表名称，默认为 None（读取第一个工作表）
    
    Returns:
        pandas.DataFrame: 读取的数据
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 '{file_path}' 不存在")
            return None
        
        # 读取 Excel 文件
        print(f"正在读取文件: {file_path}")
        
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"成功读取工作表: {sheet_name}")
        else:
            df = pd.read_excel(file_path)
            print("成功读取默认工作表")
        
        return df
    
    except FileNotFoundError:
        print(f"错误：找不到文件 '{file_path}'")
        return None
    except PermissionError:
        print(f"错误：没有权限访问文件 '{file_path}'")
        return None
    except Exception as e:
        print(f"读取文件时发生错误: {str(e)}")
        return None

def display_data_info(df):
    """
    显示数据的基本信息
    
    Args:
        df (pandas.DataFrame): 数据框
    """
    if df is None:
        return
    
    print("\n" + "="*50)
    print("数据基本信息:")
    print("="*50)
    
    # 数据形状
    print(f"数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})")
    
    # 列名
    print(f"\n列名: {list(df.columns)}")
    
    # 数据类型
    print(f"\n数据类型:")
    print(df.dtypes)
    
    # 前几行数据
    print(f"\n前5行数据:")
    print(df.head())
    
    # 基本统计信息
    print(f"\n数值列的基本统计信息:")
    numeric_cols = df.select_dtypes(include=['number']).columns
    if len(numeric_cols) > 0:
        print(df[numeric_cols].describe())
    else:
        print("没有数值列")
    
    # 缺失值信息
    print(f"\n缺失值统计:")
    missing_data = df.isnull().sum()
    if missing_data.sum() > 0:
        print(missing_data[missing_data > 0])
    else:
        print("没有缺失值")

def get_sheet_names(file_path):
    """
    获取 Excel 文件中所有工作表的名称
    
    Args:
        file_path (str): Excel 文件路径
    
    Returns:
        list: 工作表名称列表
    """
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except Exception as e:
        print(f"获取工作表名称时发生错误: {str(e)}")
        return []

def main():
    """
    主函数
    """
    # 默认文件路径
    default_file = "指标库.xlsx"
    
    print("pandas Excel 文件读取程序")
    print("="*30)
    
    # 检查默认文件是否存在
    if os.path.exists(default_file):
        file_path = default_file
        print(f"找到默认文件: {file_path}")
    else:
        # 如果默认文件不存在，让用户输入文件路径
        file_path = input("请输入 Excel 文件路径: ").strip()
        if not file_path:
            print("未输入文件路径，程序退出")
            return
    
    # 获取所有工作表名称
    sheet_names = get_sheet_names(file_path)
    if sheet_names:
        print(f"\n文件中的工作表: {sheet_names}")
        
        # 如果有多个工作表，让用户选择
        if len(sheet_names) > 1:
            print("\n选择要读取的工作表:")
            for i, name in enumerate(sheet_names, 1):
                print(f"{i}. {name}")
            
            try:
                choice = input("\n请输入工作表编号 (直接回车读取第一个): ").strip()
                if choice:
                    sheet_index = int(choice) - 1
                    if 0 <= sheet_index < len(sheet_names):
                        selected_sheet = sheet_names[sheet_index]
                    else:
                        print("无效的选择，使用第一个工作表")
                        selected_sheet = sheet_names[0]
                else:
                    selected_sheet = sheet_names[0]
            except ValueError:
                print("无效输入，使用第一个工作表")
                selected_sheet = sheet_names[0]
        else:
            selected_sheet = sheet_names[0]
    else:
        selected_sheet = None
    
    # 读取数据
    df = read_xlsx_file(file_path, selected_sheet)
    
    # 显示数据信息
    display_data_info(df)
    
    # 可选：保存为 CSV 文件
    if df is not None:
        save_csv = input("\n是否要将数据保存为 CSV 文件? (y/n): ").strip().lower()
        if save_csv in ['y', 'yes', '是']:
            csv_filename = file_path.replace('.xlsx', '.csv').replace('.xls', '.csv')
            try:
                df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                print(f"数据已保存为: {csv_filename}")
            except Exception as e:
                print(f"保存 CSV 文件时发生错误: {str(e)}")

if __name__ == "__main__":
    main()
