#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 pandas 读取 xlsx 文件示例
"""

import pandas as pd

# 基本用法示例
def basic_example():
    """基本的 xlsx 文件读取示例"""
    
    # 1. 读取整个 Excel 文件（默认读取第一个工作表）
    df = pd.read_excel('指标库.xlsx')
    print("1. 读取整个文件:")
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print(df.head())
    print("\n" + "="*50 + "\n")
    
    # 2. 读取指定工作表
    # df = pd.read_excel('指标库.xlsx', sheet_name='Sheet1')
    
    # 3. 读取多个工作表
    # all_sheets = pd.read_excel('指标库.xlsx', sheet_name=None)  # 返回字典
    
    # 4. 读取指定行和列
    # df = pd.read_excel('指标库.xlsx', usecols='A:C', nrows=10)
    
    # 5. 跳过前几行
    # df = pd.read_excel('指标库.xlsx', skiprows=2)
    
    return df

# 高级用法示例
def advanced_example():
    """高级的 xlsx 文件读取示例"""
    
    try:
        # 读取文件并设置参数
        df = pd.read_excel(
            '指标库.xlsx',
            sheet_name=0,           # 读取第一个工作表
            header=0,               # 第一行作为列名
            index_col=None,         # 不设置索引列
            usecols=None,           # 读取所有列
            skiprows=0,             # 不跳过行
            nrows=None,             # 读取所有行
            na_values=['', 'NULL', 'null', 'N/A'],  # 指定空值
            dtype=None,             # 自动推断数据类型
            engine='openpyxl'       # 指定引擎
        )
        
        print("2. 高级读取示例:")
        print(f"数据形状: {df.shape}")
        print(f"数据类型:\n{df.dtypes}")
        print(f"缺失值统计:\n{df.isnull().sum()}")
        print("\n" + "="*50 + "\n")
        
        return df
        
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return None

# 数据处理示例
def data_processing_example(df):
    """数据处理示例"""
    
    if df is None:
        return
    
    print("3. 数据处理示例:")
    
    # 基本信息
    print(f"数据概览:")
    print(df.info())
    
    # 数值列的统计信息
    numeric_cols = df.select_dtypes(include=['number']).columns
    if len(numeric_cols) > 0:
        print(f"\n数值列统计:")
        print(df[numeric_cols].describe())
    
    # 字符串列的信息
    string_cols = df.select_dtypes(include=['object']).columns
    if len(string_cols) > 0:
        print(f"\n字符串列:")
        for col in string_cols:
            print(f"{col}: {df[col].nunique()} 个唯一值")
    
    print("\n" + "="*50 + "\n")

def main():
    """主函数"""
    print("pandas 读取 xlsx 文件示例程序")
    print("="*40)
    
    # 检查文件是否存在
    import os
    if not os.path.exists('指标库.xlsx'):
        print("错误: 找不到文件 '指标库.xlsx'")
        print("请确保文件存在于当前目录中")
        return
    
    # 基本示例
    df1 = basic_example()
    
    # 高级示例
    df2 = advanced_example()
    
    # 数据处理示例
    data_processing_example(df1)
    
    # 保存处理后的数据
    if df1 is not None:
        # 保存为 CSV
        df1.to_csv('output.csv', index=False, encoding='utf-8-sig')
        print("数据已保存为 output.csv")
        
        # 保存为新的 Excel 文件
        df1.to_excel('output.xlsx', index=False)
        print("数据已保存为 output.xlsx")

if __name__ == "__main__":
    main()
